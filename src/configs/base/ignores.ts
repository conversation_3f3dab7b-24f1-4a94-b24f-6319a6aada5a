import { concat } from 'eslint-flat-config-utils'

export const ignores = () => concat({
    ignores: [
        '**/*.min.*',
        '**/*.tsbuildinfo',
        '**/.astro',
        '**/.cache',
        '**/.changeset',
        '**/.dockerignore',
        '**/.env*',
        '**/.history',
        '**/.idea',
        '**/.next',
        '**/.nuxt',
        '**/.nyc_output',
        '**/.output',
        '**/.parcel-cache',
        '**/.pnpm',
        '**/.rollup.cache',
        '**/.rush',
        '**/.secrets',
        '**/.swc',
        '**/.temp',
        '**/.tmp',
        '**/.tsbuildinfo',
        '**/.turbo',
        '**/.vercel',
        '**/.vite',
        '**/.vite-inspect',
        '**/.vitepress/cache',
        '**/.vscode',
        '**/__snapshots__',
        '**/auto-import?(s).d.ts',
        '**/bun.lockb',
        '**/CHANGELOG*.md',
        '**/components.d.ts',
        '**/coverage',
        '**/dev-dist',
        '**/dist',
        '**/LICENSE*',
        '**/node_modules',
        '**/out',
        '**/output',
        '**/package-lock.json',
        '**/playwright-report',
        '**/pnpm-lock.yaml',
        '**/pnpm-workspace.yaml',
        '**/storybook-static',
        '**/temp',
        '**/test-results',
        '**/tmp',
        '**/tsconfig.tsbuildinfo',
        '**/typed-router.d.ts',
        '**/yarn.lock',
    ],
})
