#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.28.0_jiti@2.4.2_/node_modules/@eslint/config-inspector/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.28.0_jiti@2.4.2_/node_modules/@eslint/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.28.0_jiti@2.4.2_/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.28.0_jiti@2.4.2_/node_modules/@eslint/config-inspector/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.28.0_jiti@2.4.2_/node_modules/@eslint/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.28.0_jiti@2.4.2_/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin.mjs" "$@"
else
  exec node  "$basedir/../../bin.mjs" "$@"
fi
