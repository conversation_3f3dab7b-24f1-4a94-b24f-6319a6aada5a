import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * This is a compatibility ruleset that:
 * - disables rules from eslint:recommended which are already handled by TypeScript.
 * - enables rules that make sense due to TS's typechecking / transpilation.
 * @see {@link https://typescript-eslint.io/users/configs/#eslint-recommended}
 */
declare const _default: (_plugin: FlatConfig.Plugin, _parser: FlatConfig.Parser) => FlatConfig.Config;
export default _default;
//# sourceMappingURL=eslint-recommended.d.ts.map