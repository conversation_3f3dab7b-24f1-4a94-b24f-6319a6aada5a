export type MessageIds = 'omittingRestParameter' | 'omittingSingleParameter' | 'singleParameterDifference';
export type Options = [
    {
        ignoreDifferentlyNamedParameters?: boolean;
        ignoreOverloadsWithDifferentJSDoc?: boolean;
    }
];
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=unified-signatures.d.ts.map