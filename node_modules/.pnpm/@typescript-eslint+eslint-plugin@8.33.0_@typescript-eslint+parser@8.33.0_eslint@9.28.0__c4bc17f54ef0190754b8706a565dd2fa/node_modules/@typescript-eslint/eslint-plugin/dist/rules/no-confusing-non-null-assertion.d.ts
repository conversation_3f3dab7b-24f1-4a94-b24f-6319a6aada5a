import type { TSESLint } from '@typescript-eslint/utils';
export type MessageId = 'confusingAssign' | 'confusingEqual' | 'confusingOperator' | 'notNeedInAssign' | 'notNeedInEqualTest' | 'notNeedInOperator' | 'wrapUpLeft';
declare const _default: TSESLint.RuleModule<MessageId, [], import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-confusing-non-null-assertion.d.ts.map