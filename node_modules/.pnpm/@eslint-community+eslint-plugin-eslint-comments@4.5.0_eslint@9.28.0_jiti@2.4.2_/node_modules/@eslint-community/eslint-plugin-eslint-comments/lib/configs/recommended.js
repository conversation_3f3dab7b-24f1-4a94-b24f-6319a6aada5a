/** DON'T EDIT THIS FILE; was created by scripts. */
"use strict"

module.exports = {
    plugins: ["@eslint-community/eslint-comments"],
    rules: {
        "@eslint-community/eslint-comments/disable-enable-pair": "error",
        "@eslint-community/eslint-comments/no-aggregating-enable": "error",
        "@eslint-community/eslint-comments/no-duplicate-disable": "error",
        "@eslint-community/eslint-comments/no-unlimited-disable": "error",
        "@eslint-community/eslint-comments/no-unused-enable": "error",
    },
}
