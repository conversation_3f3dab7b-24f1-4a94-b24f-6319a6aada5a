{"name": "@stylistic/eslint-plugin", "type": "module", "version": "4.4.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/eslint-stylistic/eslint-stylistic#readme", "repository": {"directory": "packages/eslint-plugin", "type": "git", "url": "git+https://github.com/eslint-stylistic/eslint-stylistic.git"}, "bugs": {"url": "https://github.com/eslint-stylistic/eslint-stylistic/issues"}, "exports": {".": {"types": "./dist/dts/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}, "./define-config-support": {"types": "./dist/dts/define-config-support.d.ts"}, "./rule-options": {"types": "./dist/dts/rule-options.d.ts"}, "./rules/array-bracket-newline": "./dist/rules/array-bracket-newline.js", "./rules/array-bracket-spacing": "./dist/rules/array-bracket-spacing.js", "./rules/array-element-newline": "./dist/rules/array-element-newline.js", "./rules/arrow-parens": "./dist/rules/arrow-parens.js", "./rules/arrow-spacing": "./dist/rules/arrow-spacing.js", "./rules/block-spacing": "./dist/rules/block-spacing.js", "./rules/brace-style": "./dist/rules/brace-style.js", "./rules/comma-dangle": "./dist/rules/comma-dangle.js", "./rules/comma-spacing": "./dist/rules/comma-spacing.js", "./rules/comma-style": "./dist/rules/comma-style.js", "./rules/computed-property-spacing": "./dist/rules/computed-property-spacing.js", "./rules/curly-newline": "./dist/rules/curly-newline.js", "./rules/dot-location": "./dist/rules/dot-location.js", "./rules/eol-last": "./dist/rules/eol-last.js", "./rules/func-call-spacing": "./dist/rules/func-call-spacing.js", "./rules/function-call-argument-newline": "./dist/rules/function-call-argument-newline.js", "./rules/function-call-spacing": "./dist/rules/function-call-spacing.js", "./rules/function-paren-newline": "./dist/rules/function-paren-newline.js", "./rules/generator-star-spacing": "./dist/rules/generator-star-spacing.js", "./rules/implicit-arrow-linebreak": "./dist/rules/implicit-arrow-linebreak.js", "./rules/indent": "./dist/rules/indent.js", "./rules/indent-binary-ops": "./dist/rules/indent-binary-ops.js", "./rules/jsx-child-element-spacing": "./dist/rules/jsx-child-element-spacing.js", "./rules/jsx-closing-bracket-location": "./dist/rules/jsx-closing-bracket-location.js", "./rules/jsx-closing-tag-location": "./dist/rules/jsx-closing-tag-location.js", "./rules/jsx-curly-brace-presence": "./dist/rules/jsx-curly-brace-presence.js", "./rules/jsx-curly-newline": "./dist/rules/jsx-curly-newline.js", "./rules/jsx-curly-spacing": "./dist/rules/jsx-curly-spacing.js", "./rules/jsx-equals-spacing": "./dist/rules/jsx-equals-spacing.js", "./rules/jsx-first-prop-new-line": "./dist/rules/jsx-first-prop-new-line.js", "./rules/jsx-function-call-newline": "./dist/rules/jsx-function-call-newline.js", "./rules/jsx-indent": "./dist/rules/jsx-indent.js", "./rules/jsx-indent-props": "./dist/rules/jsx-indent-props.js", "./rules/jsx-max-props-per-line": "./dist/rules/jsx-max-props-per-line.js", "./rules/jsx-newline": "./dist/rules/jsx-newline.js", "./rules/jsx-one-expression-per-line": "./dist/rules/jsx-one-expression-per-line.js", "./rules/jsx-pascal-case": "./dist/rules/jsx-pascal-case.js", "./rules/jsx-props-no-multi-spaces": "./dist/rules/jsx-props-no-multi-spaces.js", "./rules/jsx-quotes": "./dist/rules/jsx-quotes.js", "./rules/jsx-self-closing-comp": "./dist/rules/jsx-self-closing-comp.js", "./rules/jsx-sort-props": "./dist/rules/jsx-sort-props.js", "./rules/jsx-tag-spacing": "./dist/rules/jsx-tag-spacing.js", "./rules/jsx-wrap-multilines": "./dist/rules/jsx-wrap-multilines.js", "./rules/key-spacing": "./dist/rules/key-spacing.js", "./rules/keyword-spacing": "./dist/rules/keyword-spacing.js", "./rules/line-comment-position": "./dist/rules/line-comment-position.js", "./rules/linebreak-style": "./dist/rules/linebreak-style.js", "./rules/lines-around-comment": "./dist/rules/lines-around-comment.js", "./rules/lines-between-class-members": "./dist/rules/lines-between-class-members.js", "./rules/max-len": "./dist/rules/max-len.js", "./rules/max-statements-per-line": "./dist/rules/max-statements-per-line.js", "./rules/member-delimiter-style": "./dist/rules/member-delimiter-style.js", "./rules/multiline-comment-style": "./dist/rules/multiline-comment-style.js", "./rules/multiline-ternary": "./dist/rules/multiline-ternary.js", "./rules/new-parens": "./dist/rules/new-parens.js", "./rules/newline-per-chained-call": "./dist/rules/newline-per-chained-call.js", "./rules/no-confusing-arrow": "./dist/rules/no-confusing-arrow.js", "./rules/no-extra-parens": "./dist/rules/no-extra-parens.js", "./rules/no-extra-semi": "./dist/rules/no-extra-semi.js", "./rules/no-floating-decimal": "./dist/rules/no-floating-decimal.js", "./rules/no-mixed-operators": "./dist/rules/no-mixed-operators.js", "./rules/no-mixed-spaces-and-tabs": "./dist/rules/no-mixed-spaces-and-tabs.js", "./rules/no-multi-spaces": "./dist/rules/no-multi-spaces.js", "./rules/no-multiple-empty-lines": "./dist/rules/no-multiple-empty-lines.js", "./rules/no-tabs": "./dist/rules/no-tabs.js", "./rules/no-trailing-spaces": "./dist/rules/no-trailing-spaces.js", "./rules/no-whitespace-before-property": "./dist/rules/no-whitespace-before-property.js", "./rules/nonblock-statement-body-position": "./dist/rules/nonblock-statement-body-position.js", "./rules/object-curly-newline": "./dist/rules/object-curly-newline.js", "./rules/object-curly-spacing": "./dist/rules/object-curly-spacing.js", "./rules/object-property-newline": "./dist/rules/object-property-newline.js", "./rules/one-var-declaration-per-line": "./dist/rules/one-var-declaration-per-line.js", "./rules/operator-linebreak": "./dist/rules/operator-linebreak.js", "./rules/padded-blocks": "./dist/rules/padded-blocks.js", "./rules/padding-line-between-statements": "./dist/rules/padding-line-between-statements.js", "./rules/quote-props": "./dist/rules/quote-props.js", "./rules/quotes": "./dist/rules/quotes.js", "./rules/rest-spread-spacing": "./dist/rules/rest-spread-spacing.js", "./rules/semi": "./dist/rules/semi.js", "./rules/semi-spacing": "./dist/rules/semi-spacing.js", "./rules/semi-style": "./dist/rules/semi-style.js", "./rules/space-before-blocks": "./dist/rules/space-before-blocks.js", "./rules/space-before-function-paren": "./dist/rules/space-before-function-paren.js", "./rules/space-in-parens": "./dist/rules/space-in-parens.js", "./rules/space-infix-ops": "./dist/rules/space-infix-ops.js", "./rules/space-unary-ops": "./dist/rules/space-unary-ops.js", "./rules/spaced-comment": "./dist/rules/spaced-comment.js", "./rules/switch-colon-spacing": "./dist/rules/switch-colon-spacing.js", "./rules/template-curly-spacing": "./dist/rules/template-curly-spacing.js", "./rules/template-tag-spacing": "./dist/rules/template-tag-spacing.js", "./rules/type-annotation-spacing": "./dist/rules/type-annotation-spacing.js", "./rules/type-generic-spacing": "./dist/rules/type-generic-spacing.js", "./rules/type-named-tuple-spacing": "./dist/rules/type-named-tuple-spacing.js", "./rules/wrap-iife": "./dist/rules/wrap-iife.js", "./rules/wrap-regex": "./dist/rules/wrap-regex.js", "./rules/yield-star-spacing": "./dist/rules/yield-star-spacing.js"}, "main": "./dist/index.js", "types": "./dist/dts/index.d.ts", "files": ["dist"], "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": ">=9.0.0"}, "dependencies": {"@typescript-eslint/utils": "^8.32.1", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "estraverse": "^5.3.0", "picomatch": "^4.0.2"}, "scripts": {"build": "rimraf dist && rollup --config=rollup.config.mjs", "dev": "rollup --config=rollup.config.mjs --watch"}}