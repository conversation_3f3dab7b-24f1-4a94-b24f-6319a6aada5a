import { p as plugin, c as configs } from './configs.js';
import './utils.js';
import 'eslint-visitor-keys';
import 'espree';
import 'estraverse';
import './rules/array-bracket-newline.js';
import './rules/array-bracket-spacing.js';
import './rules/array-element-newline.js';
import './rules/arrow-parens.js';
import './rules/arrow-spacing.js';
import './rules/block-spacing.js';
import '@typescript-eslint/utils';
import '@typescript-eslint/utils/ast-utils';
import './rules/brace-style.js';
import './rules/comma-dangle.js';
import './rules/comma-spacing.js';
import './rules/comma-style.js';
import './rules/computed-property-spacing.js';
import './rules/curly-newline.js';
import './rules/dot-location.js';
import './rules/eol-last.js';
import './rules/function-call-argument-newline.js';
import './rules/function-call-spacing.js';
import './rules/function-paren-newline.js';
import './rules/generator-star-spacing.js';
import './rules/implicit-arrow-linebreak.js';
import './rules/indent-binary-ops.js';
import './rules/indent.js';
import './rules/jsx-child-element-spacing.js';
import './rules/jsx-closing-bracket-location.js';
import './rules/jsx-closing-tag-location.js';
import './rules/jsx-curly-brace-presence.js';
import './rules/jsx-curly-newline.js';
import './rules/jsx-curly-spacing.js';
import './rules/jsx-equals-spacing.js';
import './rules/jsx-first-prop-new-line.js';
import './rules/jsx-function-call-newline.js';
import './rules/jsx-indent-props.js';
import './rules/jsx-indent.js';
import './rules/jsx-max-props-per-line.js';
import './rules/jsx-newline.js';
import './rules/jsx-one-expression-per-line.js';
import './rules/jsx-pascal-case.js';
import 'picomatch';
import './rules/jsx-props-no-multi-spaces.js';
import './rules/jsx-quotes.js';
import './rules/jsx-self-closing-comp.js';
import './rules/jsx-sort-props.js';
import './rules/jsx-tag-spacing.js';
import './rules/jsx-wrap-multilines.js';
import './rules/key-spacing.js';
import './rules/keyword-spacing.js';
import './rules/line-comment-position.js';
import './rules/linebreak-style.js';
import './rules/lines-around-comment.js';
import './rules/lines-between-class-members.js';
import './rules/max-len.js';
import './rules/max-statements-per-line.js';
import './rules/member-delimiter-style.js';
import './rules/multiline-comment-style.js';
import './rules/multiline-ternary.js';
import './rules/new-parens.js';
import './rules/newline-per-chained-call.js';
import './rules/no-confusing-arrow.js';
import './rules/no-extra-parens.js';
import './rules/no-extra-semi.js';
import './rules/no-floating-decimal.js';
import './rules/no-mixed-operators.js';
import './rules/no-mixed-spaces-and-tabs.js';
import './rules/no-multi-spaces.js';
import './rules/no-multiple-empty-lines.js';
import './rules/no-tabs.js';
import './rules/no-trailing-spaces.js';
import './rules/no-whitespace-before-property.js';
import './rules/nonblock-statement-body-position.js';
import './rules/object-curly-newline.js';
import './rules/object-curly-spacing.js';
import './rules/object-property-newline.js';
import './rules/one-var-declaration-per-line.js';
import './rules/operator-linebreak.js';
import './rules/padded-blocks.js';
import './rules/padding-line-between-statements.js';
import './rules/quote-props.js';
import './rules/quotes.js';
import './rules/rest-spread-spacing.js';
import './rules/semi-spacing.js';
import './rules/semi-style.js';
import './rules/semi.js';
import './rules/space-before-blocks.js';
import './rules/space-before-function-paren.js';
import './rules/space-in-parens.js';
import './rules/space-infix-ops.js';
import './rules/space-unary-ops.js';
import './rules/spaced-comment.js';
import './vendor.js';
import './rules/switch-colon-spacing.js';
import './rules/template-curly-spacing.js';
import './rules/template-tag-spacing.js';
import './rules/type-annotation-spacing.js';
import './rules/type-generic-spacing.js';
import './rules/type-named-tuple-spacing.js';
import './rules/wrap-iife.js';
import './rules/wrap-regex.js';
import './rules/yield-star-spacing.js';

const index = Object.assign(plugin, { configs });

export { index as default, index as "module.exports" };
