import { o as createAllConfigs } from './utils.js';
import arrayBracketNewline from './rules/array-bracket-newline.js';
import arrayBracketSpacing from './rules/array-bracket-spacing.js';
import arrayElementNewline from './rules/array-element-newline.js';
import arrowParens from './rules/arrow-parens.js';
import arrowSpacing from './rules/arrow-spacing.js';
import blockSpacing from './rules/block-spacing.js';
import braceStyle from './rules/brace-style.js';
import commaDangle from './rules/comma-dangle.js';
import commaSpacing from './rules/comma-spacing.js';
import commaStyle from './rules/comma-style.js';
import computedPropertySpacing from './rules/computed-property-spacing.js';
import curlyNewline from './rules/curly-newline.js';
import dotLocation from './rules/dot-location.js';
import eolLast from './rules/eol-last.js';
import functionCallArgumentNewline from './rules/function-call-argument-newline.js';
import functionCallSpacing from './rules/function-call-spacing.js';
import functionParenNewline from './rules/function-paren-newline.js';
import generatorStarSpacing from './rules/generator-star-spacing.js';
import implicitArrowLinebreak from './rules/implicit-arrow-linebreak.js';
import indentBinaryOps from './rules/indent-binary-ops.js';
import indent from './rules/indent.js';
import jsxChildElementSpacing from './rules/jsx-child-element-spacing.js';
import jsxClosingBracketLocation from './rules/jsx-closing-bracket-location.js';
import jsxClosingTagLocation from './rules/jsx-closing-tag-location.js';
import jsxCurlyBracePresence from './rules/jsx-curly-brace-presence.js';
import jsxCurlyNewline from './rules/jsx-curly-newline.js';
import jsxCurlySpacing from './rules/jsx-curly-spacing.js';
import jsxEqualsSpacing from './rules/jsx-equals-spacing.js';
import jsxFirstPropNewLine from './rules/jsx-first-prop-new-line.js';
import jsxFunctionCallNewline from './rules/jsx-function-call-newline.js';
import jsxIndentProps from './rules/jsx-indent-props.js';
import jsxIndent from './rules/jsx-indent.js';
import jsxMaxPropsPerLine from './rules/jsx-max-props-per-line.js';
import jsxNewline from './rules/jsx-newline.js';
import jsxOneExpressionPerLine from './rules/jsx-one-expression-per-line.js';
import jsxPascalCase from './rules/jsx-pascal-case.js';
import jsxPropsNoMultiSpaces from './rules/jsx-props-no-multi-spaces.js';
import jsxQuotes from './rules/jsx-quotes.js';
import jsxSelfClosingComp from './rules/jsx-self-closing-comp.js';
import jsxSortProps from './rules/jsx-sort-props.js';
import jsxTagSpacing from './rules/jsx-tag-spacing.js';
import jsxWrapMultilines from './rules/jsx-wrap-multilines.js';
import keySpacing from './rules/key-spacing.js';
import keywordSpacing from './rules/keyword-spacing.js';
import lineCommentPosition from './rules/line-comment-position.js';
import linebreakStyle from './rules/linebreak-style.js';
import linesAroundComment from './rules/lines-around-comment.js';
import linesBetweenClassMembers from './rules/lines-between-class-members.js';
import maxLen from './rules/max-len.js';
import maxStatementsPerLine from './rules/max-statements-per-line.js';
import memberDelimiterStyle from './rules/member-delimiter-style.js';
import multilineCommentStyle from './rules/multiline-comment-style.js';
import multilineTernary from './rules/multiline-ternary.js';
import newParens from './rules/new-parens.js';
import newlinePerChainedCall from './rules/newline-per-chained-call.js';
import noConfusingArrow from './rules/no-confusing-arrow.js';
import noExtraParens from './rules/no-extra-parens.js';
import noExtraSemi from './rules/no-extra-semi.js';
import noFloatingDecimal from './rules/no-floating-decimal.js';
import noMixedOperators from './rules/no-mixed-operators.js';
import noMixedSpacesAndTabs from './rules/no-mixed-spaces-and-tabs.js';
import noMultiSpaces from './rules/no-multi-spaces.js';
import noMultipleEmptyLines from './rules/no-multiple-empty-lines.js';
import noTabs from './rules/no-tabs.js';
import noTrailingSpaces from './rules/no-trailing-spaces.js';
import noWhitespaceBeforeProperty from './rules/no-whitespace-before-property.js';
import nonblockStatementBodyPosition from './rules/nonblock-statement-body-position.js';
import objectCurlyNewline from './rules/object-curly-newline.js';
import objectCurlySpacing from './rules/object-curly-spacing.js';
import objectPropertyNewline from './rules/object-property-newline.js';
import oneVarDeclarationPerLine from './rules/one-var-declaration-per-line.js';
import operatorLinebreak from './rules/operator-linebreak.js';
import paddedBlocks from './rules/padded-blocks.js';
import paddingLineBetweenStatements from './rules/padding-line-between-statements.js';
import quoteProps from './rules/quote-props.js';
import quotes from './rules/quotes.js';
import restSpreadSpacing from './rules/rest-spread-spacing.js';
import semiSpacing from './rules/semi-spacing.js';
import semiStyle from './rules/semi-style.js';
import semi from './rules/semi.js';
import spaceBeforeBlocks from './rules/space-before-blocks.js';
import spaceBeforeFunctionParen from './rules/space-before-function-paren.js';
import spaceInParens from './rules/space-in-parens.js';
import spaceInfixOps from './rules/space-infix-ops.js';
import spaceUnaryOps from './rules/space-unary-ops.js';
import spacedComment from './rules/spaced-comment.js';
import switchColonSpacing from './rules/switch-colon-spacing.js';
import templateCurlySpacing from './rules/template-curly-spacing.js';
import templateTagSpacing from './rules/template-tag-spacing.js';
import typeAnnotationSpacing from './rules/type-annotation-spacing.js';
import typeGenericSpacing from './rules/type-generic-spacing.js';
import typeNamedTupleSpacing from './rules/type-named-tuple-spacing.js';
import wrapIife from './rules/wrap-iife.js';
import wrapRegex from './rules/wrap-regex.js';
import yieldStarSpacing from './rules/yield-star-spacing.js';

var rules = {
  "array-bracket-newline": arrayBracketNewline,
  "array-bracket-spacing": arrayBracketSpacing,
  "array-element-newline": arrayElementNewline,
  "arrow-parens": arrowParens,
  "arrow-spacing": arrowSpacing,
  "block-spacing": blockSpacing,
  "brace-style": braceStyle,
  "comma-dangle": commaDangle,
  "comma-spacing": commaSpacing,
  "comma-style": commaStyle,
  "computed-property-spacing": computedPropertySpacing,
  "curly-newline": curlyNewline,
  "dot-location": dotLocation,
  "eol-last": eolLast,
  "func-call-spacing": functionCallSpacing,
  "function-call-argument-newline": functionCallArgumentNewline,
  "function-call-spacing": functionCallSpacing,
  "function-paren-newline": functionParenNewline,
  "generator-star-spacing": generatorStarSpacing,
  "implicit-arrow-linebreak": implicitArrowLinebreak,
  "indent": indent,
  "indent-binary-ops": indentBinaryOps,
  "jsx-child-element-spacing": jsxChildElementSpacing,
  "jsx-closing-bracket-location": jsxClosingBracketLocation,
  "jsx-closing-tag-location": jsxClosingTagLocation,
  "jsx-curly-brace-presence": jsxCurlyBracePresence,
  "jsx-curly-newline": jsxCurlyNewline,
  "jsx-curly-spacing": jsxCurlySpacing,
  "jsx-equals-spacing": jsxEqualsSpacing,
  "jsx-first-prop-new-line": jsxFirstPropNewLine,
  "jsx-function-call-newline": jsxFunctionCallNewline,
  "jsx-indent": jsxIndent,
  "jsx-indent-props": jsxIndentProps,
  "jsx-max-props-per-line": jsxMaxPropsPerLine,
  "jsx-newline": jsxNewline,
  "jsx-one-expression-per-line": jsxOneExpressionPerLine,
  "jsx-pascal-case": jsxPascalCase,
  "jsx-props-no-multi-spaces": jsxPropsNoMultiSpaces,
  "jsx-quotes": jsxQuotes,
  "jsx-self-closing-comp": jsxSelfClosingComp,
  "jsx-sort-props": jsxSortProps,
  "jsx-tag-spacing": jsxTagSpacing,
  "jsx-wrap-multilines": jsxWrapMultilines,
  "key-spacing": keySpacing,
  "keyword-spacing": keywordSpacing,
  "line-comment-position": lineCommentPosition,
  "linebreak-style": linebreakStyle,
  "lines-around-comment": linesAroundComment,
  "lines-between-class-members": linesBetweenClassMembers,
  "max-len": maxLen,
  "max-statements-per-line": maxStatementsPerLine,
  "member-delimiter-style": memberDelimiterStyle,
  "multiline-comment-style": multilineCommentStyle,
  "multiline-ternary": multilineTernary,
  "new-parens": newParens,
  "newline-per-chained-call": newlinePerChainedCall,
  "no-confusing-arrow": noConfusingArrow,
  "no-extra-parens": noExtraParens,
  "no-extra-semi": noExtraSemi,
  "no-floating-decimal": noFloatingDecimal,
  "no-mixed-operators": noMixedOperators,
  "no-mixed-spaces-and-tabs": noMixedSpacesAndTabs,
  "no-multi-spaces": noMultiSpaces,
  "no-multiple-empty-lines": noMultipleEmptyLines,
  "no-tabs": noTabs,
  "no-trailing-spaces": noTrailingSpaces,
  "no-whitespace-before-property": noWhitespaceBeforeProperty,
  "nonblock-statement-body-position": nonblockStatementBodyPosition,
  "object-curly-newline": objectCurlyNewline,
  "object-curly-spacing": objectCurlySpacing,
  "object-property-newline": objectPropertyNewline,
  "one-var-declaration-per-line": oneVarDeclarationPerLine,
  "operator-linebreak": operatorLinebreak,
  "padded-blocks": paddedBlocks,
  "padding-line-between-statements": paddingLineBetweenStatements,
  "quote-props": quoteProps,
  "quotes": quotes,
  "rest-spread-spacing": restSpreadSpacing,
  "semi": semi,
  "semi-spacing": semiSpacing,
  "semi-style": semiStyle,
  "space-before-blocks": spaceBeforeBlocks,
  "space-before-function-paren": spaceBeforeFunctionParen,
  "space-in-parens": spaceInParens,
  "space-infix-ops": spaceInfixOps,
  "space-unary-ops": spaceUnaryOps,
  "spaced-comment": spacedComment,
  "switch-colon-spacing": switchColonSpacing,
  "template-curly-spacing": templateCurlySpacing,
  "template-tag-spacing": templateTagSpacing,
  "type-annotation-spacing": typeAnnotationSpacing,
  "type-generic-spacing": typeGenericSpacing,
  "type-named-tuple-spacing": typeNamedTupleSpacing,
  "wrap-iife": wrapIife,
  "wrap-regex": wrapRegex,
  "yield-star-spacing": yieldStarSpacing
};

const plugin = {
  rules
};

function customize(options = {}) {
  const {
    arrowParens = false,
    blockSpacing = true,
    braceStyle = "stroustrup",
    commaDangle = "always-multiline",
    indent = 2,
    jsx = true,
    pluginName = "@stylistic",
    quoteProps = "consistent-as-needed",
    quotes = "single",
    semi = false,
    severity = "error"
  } = options;
  let rules = {
    "@stylistic/array-bracket-spacing": [severity, "never"],
    "@stylistic/arrow-parens": [severity, arrowParens ? "always" : "as-needed", { requireForBlockBody: true }],
    "@stylistic/arrow-spacing": [severity, { after: true, before: true }],
    "@stylistic/block-spacing": [severity, blockSpacing ? "always" : "never"],
    "@stylistic/brace-style": [severity, braceStyle, { allowSingleLine: true }],
    "@stylistic/comma-dangle": [severity, commaDangle],
    "@stylistic/comma-spacing": [severity, { after: true, before: false }],
    "@stylistic/comma-style": [severity, "last"],
    "@stylistic/computed-property-spacing": [severity, "never", { enforceForClassMembers: true }],
    "@stylistic/dot-location": [severity, "property"],
    "@stylistic/eol-last": severity,
    "@stylistic/generator-star-spacing": [severity, { after: true, before: false }],
    "@stylistic/indent": [severity, indent, {
      ArrayExpression: 1,
      CallExpression: { arguments: 1 },
      flatTernaryExpressions: false,
      FunctionDeclaration: { body: 1, parameters: 1 },
      FunctionExpression: { body: 1, parameters: 1 },
      ignoreComments: false,
      ignoredNodes: [
        "TSUnionType",
        "TSIntersectionType",
        "TSTypeParameterInstantiation",
        "FunctionExpression > .params[decorators.length > 0]",
        "FunctionExpression > .params > :matches(Decorator, :not(:first-child))"
      ],
      ImportDeclaration: 1,
      MemberExpression: 1,
      ObjectExpression: 1,
      offsetTernaryExpressions: true,
      outerIIFEBody: 1,
      SwitchCase: 1,
      tabLength: indent === "tab" ? 4 : indent,
      VariableDeclarator: 1
    }],
    "@stylistic/indent-binary-ops": [severity, indent],
    "@stylistic/key-spacing": [severity, { afterColon: true, beforeColon: false }],
    "@stylistic/keyword-spacing": [severity, { after: true, before: true }],
    "@stylistic/lines-between-class-members": [severity, "always", { exceptAfterSingleLine: true }],
    "@stylistic/max-statements-per-line": [severity, { max: 1 }],
    "@stylistic/member-delimiter-style": [severity, {
      multiline: {
        delimiter: semi ? "semi" : "none",
        requireLast: semi
      },
      multilineDetection: "brackets",
      overrides: {
        interface: {
          multiline: {
            delimiter: semi ? "semi" : "none",
            requireLast: semi
          }
        }
      },
      singleline: {
        delimiter: semi ? "semi" : "comma"
      }
    }],
    "@stylistic/multiline-ternary": [severity, "always-multiline"],
    "@stylistic/new-parens": severity,
    "@stylistic/no-extra-parens": [severity, "functions"],
    "@stylistic/no-floating-decimal": severity,
    "@stylistic/no-mixed-operators": [severity, {
      allowSamePrecedence: true,
      groups: [
        ["==", "!=", "===", "!==", ">", ">=", "<", "<="],
        ["&&", "||"],
        ["in", "instanceof"]
      ]
    }],
    "@stylistic/no-mixed-spaces-and-tabs": severity,
    "@stylistic/no-multi-spaces": severity,
    "@stylistic/no-multiple-empty-lines": [severity, { max: 1, maxBOF: 0, maxEOF: 0 }],
    "@stylistic/no-tabs": indent === "tab" ? "off" : severity,
    "@stylistic/no-trailing-spaces": severity,
    "@stylistic/no-whitespace-before-property": severity,
    "@stylistic/object-curly-spacing": [severity, "always"],
    "@stylistic/operator-linebreak": [severity, "before"],
    "@stylistic/padded-blocks": [severity, { blocks: "never", classes: "never", switches: "never" }],
    "@stylistic/quote-props": [severity, quoteProps],
    "@stylistic/quotes": [severity, quotes, { allowTemplateLiterals: true, avoidEscape: false }],
    "@stylistic/rest-spread-spacing": [severity, "never"],
    "@stylistic/semi": [severity, semi ? "always" : "never"],
    "@stylistic/semi-spacing": [severity, { after: true, before: false }],
    "@stylistic/space-before-blocks": [severity, "always"],
    "@stylistic/space-before-function-paren": [severity, { anonymous: "always", asyncArrow: "always", named: "never" }],
    "@stylistic/space-in-parens": [severity, "never"],
    "@stylistic/space-infix-ops": severity,
    "@stylistic/space-unary-ops": [severity, { nonwords: false, words: true }],
    "@stylistic/spaced-comment": [severity, "always", {
      block: {
        balanced: true,
        exceptions: ["*"],
        markers: ["!"]
      },
      line: {
        exceptions: ["/", "#"],
        markers: ["/"]
      }
    }],
    "@stylistic/template-curly-spacing": severity,
    "@stylistic/template-tag-spacing": [severity, "never"],
    "@stylistic/type-annotation-spacing": [severity, {}],
    "@stylistic/type-generic-spacing": severity,
    "@stylistic/type-named-tuple-spacing": severity,
    "@stylistic/wrap-iife": [severity, "any", { functionPrototypeMethods: true }],
    "@stylistic/yield-star-spacing": [severity, { after: true, before: false }],
    ...jsx ? {
      "@stylistic/jsx-closing-bracket-location": severity,
      "@stylistic/jsx-closing-tag-location": severity,
      "@stylistic/jsx-curly-brace-presence": [severity, { propElementValues: "always" }],
      "@stylistic/jsx-curly-newline": severity,
      "@stylistic/jsx-curly-spacing": [severity, "never"],
      "@stylistic/jsx-equals-spacing": severity,
      "@stylistic/jsx-first-prop-new-line": severity,
      "@stylistic/jsx-function-call-newline": [severity, "multiline"],
      "@stylistic/jsx-indent-props": [severity, indent],
      "@stylistic/jsx-max-props-per-line": [severity, { maximum: 1, when: "multiline" }],
      "@stylistic/jsx-one-expression-per-line": [severity, { allow: "single-child" }],
      "@stylistic/jsx-quotes": severity,
      "@stylistic/jsx-tag-spacing": [
        severity,
        {
          afterOpening: "never",
          beforeClosing: "never",
          beforeSelfClosing: "always",
          closingSlash: "never"
        }
      ],
      "@stylistic/jsx-wrap-multilines": [
        severity,
        {
          arrow: "parens-new-line",
          assignment: "parens-new-line",
          condition: "parens-new-line",
          declaration: "parens-new-line",
          logical: "parens-new-line",
          prop: "parens-new-line",
          propertyValue: "parens-new-line",
          return: "parens-new-line"
        }
      ]
    } : {}
  };
  if (pluginName !== "@stylistic") {
    const regex = /^@stylistic\//;
    rules = Object.fromEntries(
      Object.entries(rules).map(([ruleName, ruleConfig]) => [
        ruleName.replace(regex, `${pluginName}/`),
        ruleConfig
      ])
    );
  }
  return {
    plugins: {
      [pluginName]: plugin
    },
    rules
  };
}

const config = {
  rules: {
    "array-bracket-newline": 0,
    "array-bracket-spacing": 0,
    "array-element-newline": 0,
    "arrow-parens": 0,
    "arrow-spacing": 0,
    "block-spacing": 0,
    "brace-style": 0,
    "comma-dangle": 0,
    "comma-spacing": 0,
    "comma-style": 0,
    "computed-property-spacing": 0,
    "dot-location": 0,
    "eol-last": 0,
    "func-call-spacing": 0,
    "function-call-argument-newline": 0,
    "function-paren-newline": 0,
    "generator-star-spacing": 0,
    "implicit-arrow-linebreak": 0,
    "indent": 0,
    "jsx-quotes": 0,
    "key-spacing": 0,
    "keyword-spacing": 0,
    "linebreak-style": 0,
    "lines-around-comment": 0,
    "lines-between-class-members": 0,
    "max-len": 0,
    "max-statements-per-line": 0,
    "multiline-ternary": 0,
    "new-parens": 0,
    "newline-per-chained-call": 0,
    "no-confusing-arrow": 0,
    "no-extra-parens": 0,
    "no-extra-semi": 0,
    "no-floating-decimal": 0,
    "no-mixed-operators": 0,
    "no-mixed-spaces-and-tabs": 0,
    "no-multi-spaces": 0,
    "no-multiple-empty-lines": 0,
    "no-tabs": 0,
    "no-trailing-spaces": 0,
    "no-whitespace-before-property": 0,
    "nonblock-statement-body-position": 0,
    "object-curly-newline": 0,
    "object-curly-spacing": 0,
    "object-property-newline": 0,
    "one-var-declaration-per-line": 0,
    "operator-linebreak": 0,
    "padded-blocks": 0,
    "padding-line-between-statements": 0,
    "quote-props": 0,
    "quotes": 0,
    "rest-spread-spacing": 0,
    "semi": 0,
    "semi-spacing": 0,
    "semi-style": 0,
    "space-before-blocks": 0,
    "space-before-function-paren": 0,
    "space-in-parens": 0,
    "space-infix-ops": 0,
    "space-unary-ops": 0,
    "spaced-comment": 0,
    "switch-colon-spacing": 0,
    "template-curly-spacing": 0,
    "template-tag-spacing": 0,
    "wrap-iife": 0,
    "wrap-regex": 0,
    "yield-star-spacing": 0,
    "@typescript-eslint/block-spacing": 0,
    "@typescript-eslint/brace-style": 0,
    "@typescript-eslint/comma-dangle": 0,
    "@typescript-eslint/comma-spacing": 0,
    "@typescript-eslint/func-call-spacing": 0,
    "@typescript-eslint/indent": 0,
    "@typescript-eslint/key-spacing": 0,
    "@typescript-eslint/keyword-spacing": 0,
    "@typescript-eslint/lines-around-comment": 0,
    "@typescript-eslint/lines-between-class-members": 0,
    "@typescript-eslint/member-delimiter-style": 0,
    "@typescript-eslint/no-extra-parens": 0,
    "@typescript-eslint/no-extra-semi": 0,
    "@typescript-eslint/object-curly-spacing": 0,
    "@typescript-eslint/padding-line-between-statements": 0,
    "@typescript-eslint/quotes": 0,
    "@typescript-eslint/semi": 0,
    "@typescript-eslint/space-before-blocks": 0,
    "@typescript-eslint/space-before-function-paren": 0,
    "@typescript-eslint/space-infix-ops": 0,
    "@typescript-eslint/type-annotation-spacing": 0,
    "react/jsx-child-element-spacing": 0,
    "react/jsx-closing-bracket-location": 0,
    "react/jsx-closing-tag-location": 0,
    "react/jsx-curly-brace-presence": 0,
    "react/jsx-curly-newline": 0,
    "react/jsx-curly-spacing": 0,
    "react/jsx-equals-spacing": 0,
    "react/jsx-first-prop-new-line": 0,
    "react/jsx-indent": 0,
    "react/jsx-indent-props": 0,
    "react/jsx-max-props-per-line": 0,
    "react/jsx-newline": 0,
    "react/jsx-one-expression-per-line": 0,
    "react/jsx-props-no-multi-spaces": 0,
    "react/jsx-self-closing-comp": 0,
    "react/jsx-sort-props": 0,
    "react/jsx-tag-spacing": 0,
    "react/jsx-wrap-multilines": 0
  }
};

const allConfigsIgnore = [
  // Exclude all JSX rules
  /^jsx-/,
  // https://github.com/eslint-stylistic/eslint-stylistic/pull/548
  /^curly-newline$/
];
const all = /* @__PURE__ */ createAllConfigs(plugin, "@stylistic", (name) => !allConfigsIgnore.some((re) => re.test(name)));
const recommended = /* @__PURE__ */ customize();
const configs = {
  "disable-legacy": config,
  "customize": customize,
  "recommended": recommended,
  "recommended-flat": recommended,
  "all": all,
  "all-flat": all
};

export { configs as c, plugin as p };
