{"name": "@nodelib/fs.walk", "version": "3.0.1", "description": "A library for efficiently walking a directory recursively", "license": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk", "keywords": ["NodeLib", "fs", "FileSystem", "file system", "walk", "scanner", "crawler"], "engines": {"node": ">=18.18.0"}, "files": ["out/**", "!out/benchmark", "!out/**/*.map", "!out/**/*.spec.*", "!out/**/tests/**"], "main": "out/index.js", "typings": "out/index.d.ts", "exports": {".": {"default": "./out/index.js"}, "./promises": {"default": "./out/walk-promises.js"}}, "scripts": {"clean": "rimraf {tsconfig.tsbuildinfo,out}", "lint": "eslint \"src/**/*.ts\" --cache", "compile": "tsc -b .", "compile:watch": "tsc -b . --watch --sourceMap", "test": "mocha \"out/**/*.spec.js\" -s 0", "build": "npm run clean && npm run compile && npm run lint && npm test", "watch": "npm run clean && npm run compile:watch", "bench": "npm run bench:sync && npm run bench:async && npm run bench:stream", "bench:sync": "hereby bench:sync", "bench:async": "hereby bench:async", "bench:stream": "hereby bench:stream"}, "dependencies": {"@nodelib/fs.scandir": "4.0.1", "fastq": "^1.15.0"}, "devDependencies": {"@nodelib/fs.macchiato": "3.0.0", "@nodelib/fs.walk.previous": "npm:@nodelib/fs.walk@2"}, "gitHead": "4750471fdceb6b90ac7c58a93872d61e3f7f143b"}