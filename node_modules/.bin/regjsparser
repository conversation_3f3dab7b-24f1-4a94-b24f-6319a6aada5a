#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/bin/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/regjsparser@0.12.0/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/bin/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/regjsparser@0.12.0/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../regjsparser/bin/parser" "$@"
else
  exec node  "$basedir/../regjsparser/bin/parser" "$@"
fi
