import { composer } from 'eslint-flat-config-utils'
import command from 'eslint-plugin-command/config'
import { ignores } from './src'
import { antfu } from './src/configs/base/antfu'
import { comments } from './src/configs/base/comments'
import { importX } from './src/configs/base/import-x'
import { javascript } from './src/configs/base/javascript'
import { jsonc } from './src/configs/base/jsonc'
import { kdt } from './src/configs/base/kdt'
import { node } from './src/configs/base/node'
import { promise } from './src/configs/base/promise'
import { regexp } from './src/configs/base/regexp'
import { stylistic } from './src/configs/base/stylistic'
import { typescript } from './src/configs/base/typescript'
import { unusedImports } from './src/configs/base/unused-imports'
import { format } from './src/configs/tools/format'
import { gitignore } from './src/configs/tools/gitignore'
import { perfectionist } from './src/configs/tools/perfectionist'
import { sonar } from './src/configs/tools/sonar'
import { unicorn } from './src/configs/tools/unicorn'

export default composer(
    ignores(),
    gitignore(),
    javascript(),
    typescript({ tsconfig: { tsconfigRootDir: import.meta.dirname } }),
    stylistic(),
    command(),
    antfu(),
    comments(),
    kdt(),
    unusedImports(),
    node(),
    promise(),
    sonar(),
    unicorn(),
    importX(),
    jsonc(),
    regexp(),
    perfectionist(),
    format(),
)
