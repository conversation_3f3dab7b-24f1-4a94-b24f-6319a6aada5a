// src/configs/base/antfu.ts
import { concat } from "eslint-flat-config-utils";
import plugin from "eslint-plugin-antfu";
var antfu = () => concat([
  {
    plugins: {
      antfu: plugin
    },
    rules: {
      "antfu/if-newline": "error",
      "antfu/import-dedupe": "error",
      "antfu/no-import-dist": "error",
      "antfu/no-import-node-modules-by-path": "error"
    }
  },
  {
    files: ["**/bin/**/*"],
    rules: {
      "antfu/no-import-dist": "off",
      "antfu/no-import-node-modules-by-path": "off"
    }
  }
]);

// src/configs/base/comments.ts
import configs from "@eslint-community/eslint-plugin-eslint-comments/configs";
import { concat as concat2 } from "eslint-flat-config-utils";
var comments = () => concat2(configs.recommended, {
  files: ["**/*.d.ts"],
  rules: {
    "@eslint-community/eslint-comments/no-unlimited-disable": "off"
  }
});

// src/configs/base/ignores.ts
import { concat as concat3 } from "eslint-flat-config-utils";
var ignores = () => concat3({
  ignores: [
    "**/*.min.*",
    "**/*.tsbuildinfo",
    "**/.astro",
    "**/.cache",
    "**/.changeset",
    "**/.dockerignore",
    "**/.env*",
    "**/.history",
    "**/.idea",
    "**/.next",
    "**/.nuxt",
    "**/.nyc_output",
    "**/.output",
    "**/.parcel-cache",
    "**/.pnpm",
    "**/.rollup.cache",
    "**/.rush",
    "**/.secrets",
    "**/.swc",
    "**/.temp",
    "**/.tmp",
    "**/.tsbuildinfo",
    "**/.turbo",
    "**/.vercel",
    "**/.vite",
    "**/.vite-inspect",
    "**/.vitepress/cache",
    "**/.vscode",
    "**/__snapshots__",
    "**/auto-import?(s).d.ts",
    "**/bun.lockb",
    "**/CHANGELOG*.md",
    "**/components.d.ts",
    "**/coverage",
    "**/dev-dist",
    "**/dist",
    "**/LICENSE*",
    "**/node_modules",
    "**/out",
    "**/output",
    "**/package-lock.json",
    "**/playwright-report",
    "**/pnpm-lock.yaml",
    "**/storybook-static",
    "**/temp",
    "**/test-results",
    "**/tmp",
    "**/tsconfig.tsbuildinfo",
    "**/typed-router.d.ts",
    "**/yarn.lock"
  ]
});

// src/configs/base/import-x.ts
import { concat as concat4 } from "eslint-flat-config-utils";
import { importX as plugin2 } from "eslint-plugin-import-x";
var pluginConfigs = [plugin2.flatConfigs.recommended, plugin2.flatConfigs.typescript];
var importX = () => concat4(
  ...pluginConfigs,
  {
    rules: {
      "import-x/first": "error",
      "import-x/newline-after-import": ["error", { considerComments: true, count: 1 }],
      "import-x/no-duplicates": "error",
      "import-x/no-empty-named-blocks": "error",
      "import-x/no-mutable-exports": "error",
      "import-x/no-named-as-default": "off",
      "import-x/no-named-as-default-member": "off",
      "import-x/no-named-default": "error",
      "import-x/no-self-import": "error",
      "import-x/no-unresolved": "off",
      "import-x/no-unused-modules": "error",
      "import-x/no-useless-path-segments": "error",
      "import-x/no-webpack-loader-syntax": "error",
      "import-x/order": "error"
    }
  },
  {
    files: ["**/*.d.ts"],
    rules: {
      "import-x/no-duplicates": "off"
    }
  }
);

// src/configs/base/javascript.ts
import js from "@eslint/js";
import { concat as concat5 } from "eslint-flat-config-utils";
import globals from "globals";
var rules = {
  "accessor-pairs": ["error", { enforceForClassMembers: true, setWithoutGet: true }],
  "array-callback-return": "error",
  "block-scoped-var": "error",
  "curly": ["error", "all"],
  "default-case-last": "error",
  "default-param-last": "off",
  "eqeqeq": ["error", "smart"],
  "grouped-accessor-pairs": "error",
  "logical-assignment-operators": "error",
  "new-cap": "off",
  "no-alert": "error",
  "no-array-constructor": "error",
  "no-caller": "error",
  "no-console": ["error", { allow: ["warn", "error"] }],
  "no-else-return": ["error", { allowElseIf: true }],
  "no-empty": ["error", { allowEmptyCatch: true }],
  "no-eval": "error",
  "no-extend-native": "error",
  "no-extra-bind": "error",
  "no-extra-label": "error",
  "no-fallthrough": ["error", { allowEmptyCase: true, commentPattern: String.raw`break[\s\w]*omitted` }],
  "no-implied-eval": "error",
  "no-iterator": "error",
  "no-label-var": "error",
  "no-labels": ["error", { allowLoop: false, allowSwitch: false }],
  "no-lone-blocks": "error",
  "no-multi-str": "error",
  "no-new": "error",
  "no-new-func": "error",
  "no-new-wrappers": "error",
  "no-octal-escape": "error",
  "no-proto": "error",
  "no-redeclare": ["error", { builtinGlobals: false }],
  "no-restricted-globals": ["error", { message: "Use `globalThis` instead.", name: "global" }, { message: "Use `globalThis` instead.", name: "self" }],
  "no-restricted-properties": ["error", { message: "Use `Object.getPrototypeOf` or `Object.setPrototypeOf` instead.", property: "__proto__" }, { message: "Use `Object.defineProperty` instead.", property: "__defineGetter__" }, { message: "Use `Object.defineProperty` instead.", property: "__defineSetter__" }, { message: "Use `Object.getOwnPropertyDescriptor` instead.", property: "__lookupGetter__" }, { message: "Use `Object.getOwnPropertyDescriptor` instead.", property: "__lookupSetter__" }],
  "no-restricted-syntax": ["error", "DebuggerStatement", "LabeledStatement", "WithStatement", "TSEnumDeclaration[const=true]", "TSExportAssignment"],
  "no-self-compare": "error",
  "no-sequences": "error",
  "no-template-curly-in-string": "error",
  "no-throw-literal": "error",
  "no-undef-init": "error",
  "no-unmodified-loop-condition": "error",
  "no-unneeded-ternary": ["error", { defaultAssignment: false }],
  "no-unreachable-loop": "error",
  "no-unused-expressions": ["error", { allowShortCircuit: true, allowTaggedTemplates: true, allowTernary: true }],
  "no-unused-vars": ["error", { args: "none", caughtErrors: "none", ignoreRestSiblings: true, vars: "all" }],
  "no-use-before-define": ["error", { classes: false, functions: false, variables: true }],
  "no-useless-call": "error",
  "no-useless-computed-key": ["error", { enforceForClassMembers: true }],
  "no-useless-concat": "error",
  "no-useless-constructor": "error",
  "no-useless-rename": "error",
  "no-useless-return": "error",
  "no-var": "error",
  "object-shorthand": ["error", "properties", { avoidQuotes: true }],
  "one-var": ["error", { initialized: "never" }],
  "operator-assignment": "error",
  "prefer-arrow-callback": ["error", { allowNamedFunctions: false, allowUnboundThis: true }],
  "prefer-const": ["error", { destructuring: "all", ignoreReadBeforeAssign: true }],
  "prefer-exponentiation-operator": "error",
  "prefer-promise-reject-errors": ["error", { allowEmptyReject: true }],
  "prefer-regex-literals": ["error", { disallowRedundantWrapping: true }],
  "prefer-rest-params": "error",
  "prefer-spread": "error",
  "prefer-template": "error",
  "sort-imports": ["error", { allowSeparatedGroups: false, ignoreCase: false, ignoreDeclarationSort: true, ignoreMemberSort: false, memberSyntaxSortOrder: ["none", "all", "multiple", "single"] }],
  "symbol-description": "error",
  "unicode-bom": "error",
  "vars-on-top": "error",
  "yoda": "error"
};
var config = {
  languageOptions: {
    sourceType: "module",
    ecmaVersion: 2022,
    globals: {
      ...globals.browser,
      ...globals.es2021,
      ...globals.node,
      document: "readonly",
      navigator: "readonly",
      window: "readonly"
    },
    parserOptions: {
      ecmaFeatures: { jsx: true },
      ecmaVersion: 2022,
      sourceType: "module"
    }
  },
  linterOptions: {
    reportUnusedDisableDirectives: true
  },
  rules
};
var configForScriptsFolder = {
  files: [`scripts/**/*.?([cm])[jt]s?(x)`],
  rules: {
    "no-console": "off"
  }
};
var configForTestFiles = {
  files: ["**/*.{test,spec}.?([cm])[jt]s?(x)"],
  rules: {
    "no-unused-expressions": "off"
  }
};
var javascript = () => concat5(js.configs.recommended, config, configForScriptsFolder, configForTestFiles);

// src/configs/base/jsonc.ts
import { concat as concat6 } from "eslint-flat-config-utils";
import plugin3 from "eslint-plugin-jsonc";
var jsonc = () => concat6(
  plugin3.configs["flat/recommended-with-json"],
  plugin3.configs["flat/recommended-with-json5"],
  plugin3.configs["flat/recommended-with-jsonc"],
  {
    rules: {
      "jsonc/comma-dangle": ["error", "never"],
      "jsonc/quotes": ["error", "double"]
    }
  },
  {
    files: ["**/package.json"],
    rules: {
      "jsonc/sort-array-values": ["error", { order: { type: "asc" }, pathPattern: "^files$" }],
      "jsonc/sort-keys": ["error", { order: ["publisher", "name", "displayName", "type", "version", "private", "packageManager", "description", "author", "license", "man", "directories", "funding", "homepage", "repository", "bugs", "keywords", "os", "cpu", "categories", "sideEffects", "exports", "main", "module", "unpkg", "jsdelivr", "types", "typesVersions", "bin", "icon", "files", "engines", "workspaces", "volta", "activationEvents", "contributes", "scripts", "peerDependencies", "peerDependenciesMeta", "dependencies", "optionalDependencies", "devDependencies", "bundledDependencies", "dependenciesMeta", "pnpm", "publishConfig", "config", "overrides", "resolutions", "husky", "simple-git-hooks", "lint-staged", "eslintConfig", "preferGlobal"], pathPattern: "^$" }, { order: { type: "asc" }, pathPattern: "^(?:dev|peer|optional|bundled)?[Dd]ependencies(Meta)?$" }, { order: { type: "asc" }, pathPattern: "^(?:resolutions|overrides|pnpm.overrides)$" }, { order: ["types", "import", "require", "default"], pathPattern: "^exports.*$" }]
    }
  },
  {
    files: ["**/tsconfig.json", "**/tsconfig.*.json"],
    rules: {
      "jsonc/sort-keys": ["error", { order: ["extends", "compilerOptions", "compileOnSave", "watchOptions", "buildOptions", "references", "files", "include", "exclude", "ts-node"], pathPattern: "^$" }, { order: ["incremental", "composite", "tsBuildInfoFile", "disableSourceOfProjectReferenceRedirect", "disableSolutionSearching", "disableReferencedProjectLoad", "target", "jsx", "jsxFactory", "jsxFragmentFactory", "jsxImportSource", "lib", "moduleDetection", "noLib", "reactNamespace", "useDefineForClassFields", "emitDecoratorMetadata", "experimentalDecorators", "baseUrl", "rootDir", "rootDirs", "customConditions", "module", "moduleResolution", "moduleSuffixes", "noResolve", "paths", "resolveJsonModule", "resolvePackageJsonExports", "resolvePackageJsonImports", "typeRoots", "types", "allowArbitraryExtensions", "allowImportingTsExtensions", "allowUmdGlobalAccess", "ignoreDeprecations", "allowJs", "checkJs", "maxNodeModuleJsDepth", "strict", "strictBindCallApply", "strictFunctionTypes", "strictNullChecks", "strictPropertyInitialization", "allowUnreachableCode", "allowUnusedLabels", "alwaysStrict", "exactOptionalPropertyTypes", "noFallthroughCasesInSwitch", "noImplicitAny", "noImplicitOverride", "noImplicitReturns", "noImplicitThis", "noPropertyAccessFromIndexSignature", "noUncheckedIndexedAccess", "noUnusedLocals", "noUnusedParameters", "useUnknownInCatchVariables", "declaration", "declarationDir", "declarationMap", "downlevelIteration", "emitBOM", "emitDeclarationOnly", "importHelpers", "importsNotUsedAsValues", "inlineSourceMap", "inlineSources", "mapRoot", "newLine", "noEmit", "noEmitHelpers", "noEmitOnError", "outDir", "outFile", "preserveConstEnums", "preserveValueImports", "removeComments", "sourceMap", "sourceRoot", "stripInternal", "allowSyntheticDefaultImports", "esModuleInterop", "forceConsistentCasingInFileNames", "isolatedModules", "preserveSymlinks", "verbatimModuleSyntax", "skipDefaultLibCheck", "skipLibCheck"], pathPattern: "^compilerOptions$" }]
    }
  }
);

// src/configs/base/kdt.ts
import { concat as concat7 } from "eslint-flat-config-utils";

// package.json
var version = "0.0.0";

// src/plugins/kdt/rules/arrow-empty-body-newline.ts
import { AST_NODE_TYPES as AST_NODE_TYPES2, ASTUtils } from "@typescript-eslint/utils";

// src/utils/nodes.ts
import { AST_NODE_TYPES } from "@typescript-eslint/utils";
function isBlockStatement(node2) {
  return node2?.type === AST_NODE_TYPES.BlockStatement;
}

// src/utils/rules.ts
import { RuleCreator } from "@typescript-eslint/utils/eslint-utils";
function createRule(rule) {
  return RuleCreator.withoutDocs(rule);
}

// src/plugins/kdt/rules/arrow-empty-body-newline.ts
var arrowEmptyBodyNewline = createRule({
  meta: {
    type: "layout",
    fixable: "whitespace",
    schema: [],
    messages: {
      unexpectedNewLine: "Unexpected newline between empty arrow function body"
    }
  },
  defaultOptions: [],
  create({ sourceCode, report }) {
    return {
      [AST_NODE_TYPES2.ArrowFunctionExpression](node2) {
        const inlineComments = sourceCode.getCommentsInside(node2.body);
        if (inlineComments.length === 0 && isBlockStatement(node2.body) && node2.body.body.length === 0) {
          const openBracket = sourceCode.getFirstToken(node2.body);
          const closeBracket = sourceCode.getLastToken(node2.body);
          if (openBracket && closeBracket && !ASTUtils.isTokenOnSameLine(openBracket, closeBracket)) {
            report({ node: node2, loc: node2.body.loc, messageId: "unexpectedNewLine", fix: (fixer) => fixer.replaceTextRange(node2.body.range, "{}") });
          }
        }
      }
    };
  }
});

// src/plugins/kdt/rules/import-single-line.ts
import { AST_NODE_TYPES as AST_NODE_TYPES3 } from "@typescript-eslint/utils";
var LINE_BREAK_REGEX = /[\n\r]+/;
var importSingleLine = createRule({
  meta: {
    type: "layout",
    fixable: "code",
    schema: [],
    messages: { unexpectedLineBreak: "Remove line break in the import statement" }
  },
  defaultOptions: [],
  create({ sourceCode, report }) {
    return {
      [AST_NODE_TYPES3.ImportDeclaration](node2) {
        const source = sourceCode.getText(node2);
        if (!LINE_BREAK_REGEX.test(source)) {
          return;
        }
        report({ node: node2, messageId: "unexpectedLineBreak", fix: (fixer) => fixer.replaceText(node2, source.replace(LINE_BREAK_REGEX, "")) });
      }
    };
  }
});

// src/plugins/kdt/rules/object-curly-newline.ts
import stylistic from "@stylistic/eslint-plugin";
import { ASTUtils as ASTUtils2 } from "@typescript-eslint/utils";
var baseRule = stylistic.rules["object-curly-newline"];
var objectCurlyNewline = createRule({
  meta: baseRule.meta,
  defaultOptions: [
    {
      ObjectExpression: { multiline: true },
      ObjectPattern: { multiline: true, consistent: true },
      ImportDeclaration: "never",
      ExportDeclaration: "never"
    }
  ],
  create(context, [options]) {
    function getRules(opts) {
      const contextWithDefaults = Object.create(context, {
        options: { writable: false, configurable: false, value: [opts] }
      });
      return baseRule.create(contextWithDefaults, [opts]);
    }
    function getMinProperties() {
      if (!options || typeof options === "string") {
        return false;
      }
      if ("multiline" in options && options.multiline) {
        return options.minProperties ?? Number.POSITIVE_INFINITY;
      }
      if (!("ObjectExpression" in options) || typeof options.ObjectExpression === "string") {
        return false;
      }
      if (!options.ObjectExpression?.multiline) {
        return false;
      }
      return options.ObjectExpression.minProperties ?? Number.POSITIVE_INFINITY;
    }
    const rules3 = getRules(options);
    const minProperties = getMinProperties();
    return {
      ...rules3,
      ObjectExpression(node2) {
        if (minProperties === false || node2.properties.length >= minProperties) {
          return rules3.ObjectExpression?.(node2);
        }
        const source = context.sourceCode;
        const openBrace = source.getFirstToken(node2, (token) => token.value === "{");
        const closeBrace = source.getLastToken(node2, (token) => token.value === "}");
        if (!openBrace || !closeBrace) {
          return rules3.ObjectExpression?.(node2);
        }
        let first = source.getTokenAfter(openBrace, { includeComments: true });
        let last = source.getTokenBefore(closeBrace, { includeComments: true });
        if (!first || !last) {
          return rules3.ObjectExpression?.(node2);
        }
        if (!(node2.properties.length > 0 && first.loc.start.line !== last.loc.end.line)) {
          first = source.getTokenAfter(openBrace);
          last = source.getTokenBefore(closeBrace);
          if (!first || !last) {
            return rules3.ObjectExpression?.(node2);
          }
          const hasLineBreakBetweenOpenBraceAndFirst = !ASTUtils2.isTokenOnSameLine(openBrace, first);
          const hasLineBreakBetweenCloseBraceAndLast = !ASTUtils2.isTokenOnSameLine(last, closeBrace);
          if (hasLineBreakBetweenOpenBraceAndFirst || hasLineBreakBetweenCloseBraceAndLast) {
            return getRules("always").ObjectExpression?.(node2);
          }
        }
        return rules3.ObjectExpression?.(node2);
      }
    };
  }
});

// src/plugins/kdt/index.ts
var plugin4 = {
  meta: {
    name: "kdt",
    version
  },
  rules: {
    "arrow-empty-body-newline": arrowEmptyBodyNewline,
    "import-single-line": importSingleLine,
    "object-curly-newline": objectCurlyNewline
  }
};
var kdt_default = plugin4;

// src/configs/base/kdt.ts
var kdt = () => concat7({
  plugins: {
    kdt: kdt_default
  },
  rules: {
    "kdt/arrow-empty-body-newline": "error",
    "kdt/import-single-line": "error",
    "kdt/object-curly-newline": "error",
    "@stylistic/object-curly-newline": "off"
  }
});

// src/configs/base/node.ts
import { concat as concat8 } from "eslint-flat-config-utils";
import plugin5 from "eslint-plugin-n";
var node = () => concat8(plugin5.configs["flat/recommended"], {
  rules: {
    "n/handle-callback-err": ["error", "^(err|error)$"],
    "n/hashbang": "off",
    "n/no-missing-import": "off",
    "n/no-missing-require": "off",
    "n/no-new-require": "error",
    "n/no-path-concat": "error",
    "n/no-process-exit": "off",
    "n/no-unpublished-import": "off",
    "n/no-unpublished-require": "off",
    "n/no-unsupported-features/node-builtins": "off",
    "n/shebang": "off"
  }
});

// src/configs/base/promise.ts
import { concat as concat9 } from "eslint-flat-config-utils";
import plugin6 from "eslint-plugin-promise";
var promise = () => concat9(plugin6.configs["flat/recommended"], {
  rules: {
    "promise/always-return": "off",
    "promise/catch-or-return": "off",
    "promise/no-multiple-resolved": "error",
    "promise/no-nesting": "off",
    "promise/no-promise-in-callback": "off"
  }
});

// src/configs/base/regexp.ts
import { concat as concat10 } from "eslint-flat-config-utils";
import * as plugin7 from "eslint-plugin-regexp";
var regexp = () => concat10(plugin7.configs["flat/recommended"]);

// src/configs/base/stylistic.ts
import stylisticPlugin from "@stylistic/eslint-plugin";
import { concat as concat11 } from "eslint-flat-config-utils";
var commaDangleConfig = {
  arrays: "always-multiline",
  exports: "never",
  functions: "always-multiline",
  imports: "never",
  objects: "always-multiline",
  enums: "always-multiline",
  generics: "never",
  tuples: "never"
};
var linesAroundCommentConfig = {
  allowArrayStart: true,
  allowBlockStart: true,
  allowClassStart: true,
  allowObjectStart: true,
  beforeBlockComment: true,
  beforeLineComment: true
};
var paddingLineBetweenStatementsConfig = [
  { blankLine: "never", next: ["break", "default"], prev: "*" },
  { blankLine: "never", next: "*", prev: ["break", "case", "default"] },
  { blankLine: "never", next: "case", prev: "switch" },
  { blankLine: "always", next: "interface", prev: "*" },
  { blankLine: "always", next: "*", prev: "interface" },
  { blankLine: "always", next: "class", prev: "*" },
  { blankLine: "always", next: "*", prev: "class" },
  { blankLine: "always", next: "*", prev: "directive" },
  { blankLine: "always", next: "*", prev: ["do", "for", "while"] },
  { blankLine: "always", next: ["do", "for", "while"], prev: "*" },
  { blankLine: "always", next: "*", prev: "function" },
  { blankLine: "always", next: "function", prev: "directive" },
  { blankLine: "always", next: "*", prev: "if" },
  { blankLine: "always", next: "if", prev: "*" },
  { blankLine: "always", next: "*", prev: ["multiline-block-like", "multiline-expression"] },
  { blankLine: "always", next: ["multiline-block-like", "multiline-expression"], prev: "*" },
  { blankLine: "always", next: "*", prev: ["multiline-const", "multiline-let", "multiline-var"] },
  { blankLine: "always", next: ["multiline-const", "multiline-let", "multiline-var"], prev: "*" },
  { blankLine: "always", next: "return", prev: "*" },
  { blankLine: "always", next: "*", prev: "switch" },
  { blankLine: "always", next: "switch", prev: "*" },
  { blankLine: "always", next: "*", prev: "try" },
  { blankLine: "always", next: "try", prev: "*" },
  { blankLine: "always", next: "*", prev: "with" },
  { blankLine: "always", next: "with", prev: "*" }
];
var plugin8 = stylisticPlugin.configs.customize({
  indent: 4,
  arrowParens: true,
  braceStyle: "1tbs",
  quoteProps: "consistent"
});
var stylistic2 = () => concat11(stylisticPlugin.configs["disable-legacy"], plugin8, {
  rules: {
    "@stylistic/array-bracket-newline": ["error", "consistent"],
    "@stylistic/array-element-newline": ["error", "consistent"],
    "@stylistic/comma-dangle": ["error", commaDangleConfig],
    "@stylistic/func-call-spacing": "error",
    "@stylistic/function-call-argument-newline": ["error", "consistent"],
    "@stylistic/function-call-spacing": "error",
    "@stylistic/function-paren-newline": ["error", "multiline-arguments"],
    "@stylistic/generator-star-spacing": ["error", "both"],
    "@stylistic/implicit-arrow-linebreak": "error",
    "@stylistic/jsx-child-element-spacing": "off",
    "@stylistic/jsx-newline": "off",
    "@stylistic/jsx-props-no-multi-spaces": "off",
    "@stylistic/jsx-quotes": ["error", "prefer-double"],
    "@stylistic/jsx-self-closing-comp": "off",
    "@stylistic/jsx-sort-props": "off",
    "@stylistic/linebreak-style": ["error", "unix"],
    "@stylistic/lines-around-comment": ["error", linesAroundCommentConfig],
    "@stylistic/multiline-ternary": ["error", "never"],
    "@stylistic/newline-per-chained-call": "off",
    "@stylistic/no-confusing-arrow": "error",
    "@stylistic/no-extra-semi": "error",
    "@stylistic/nonblock-statement-body-position": ["error", "below"],
    "@stylistic/object-curly-newline": ["error", { consistent: true, multiline: true }],
    "@stylistic/object-property-newline": ["error", { allowAllPropertiesOnSameLine: true }],
    "@stylistic/one-var-declaration-per-line": "off",
    "@stylistic/operator-linebreak": ["error", "after"],
    "@stylistic/padding-line-between-statements": ["error", ...paddingLineBetweenStatementsConfig],
    "@stylistic/semi-style": "error",
    "@stylistic/switch-colon-spacing": "error",
    "@stylistic/wrap-regex": "off"
  }
});

// src/configs/base/typescript.ts
import { isNullish } from "@kdt310722/utils/common";
import { concat as concat12 } from "eslint-flat-config-utils";
import tseslint from "typescript-eslint";
var rules2 = {
  "no-use-before-define": "off",
  "@typescript-eslint/array-type": ["error", { default: "array-simple" }],
  "@typescript-eslint/ban-ts-comment": ["error", { "ts-ignore": "allow-with-description" }],
  "@typescript-eslint/consistent-type-assertions": "off",
  "@typescript-eslint/consistent-type-definitions": "off",
  "@typescript-eslint/consistent-type-imports": ["error", { disallowTypeAnnotations: false, prefer: "type-imports", fixStyle: "inline-type-imports" }],
  "@typescript-eslint/dot-notation": "off",
  "@typescript-eslint/no-confusing-void-expression": "off",
  "@typescript-eslint/no-dynamic-delete": "off",
  "@typescript-eslint/no-empty-function": "off",
  "@typescript-eslint/no-explicit-any": "off",
  "@typescript-eslint/no-extraneous-class": "off",
  "@typescript-eslint/no-import-type-side-effects": "error",
  "@typescript-eslint/no-invalid-void-type": "off",
  "@typescript-eslint/no-misused-promises": "off",
  "@typescript-eslint/no-non-null-assertion": "off",
  "@typescript-eslint/no-redundant-type-constituents": "off",
  "@typescript-eslint/no-unnecessary-condition": "off",
  "@typescript-eslint/no-unused-vars": ["error", { args: "all", argsIgnorePattern: "^_", caughtErrors: "all", caughtErrorsIgnorePattern: "^_", destructuredArrayIgnorePattern: "^_", vars: "all", varsIgnorePattern: "^_", ignoreRestSiblings: true }],
  "@typescript-eslint/no-use-before-define": ["error", { classes: false, functions: false, variables: true }],
  "@typescript-eslint/no-useless-constructor": "off",
  "@typescript-eslint/no-useless-empty-export": "error",
  "@typescript-eslint/no-var-requires": "off",
  "@typescript-eslint/triple-slash-reference": "off",
  "@typescript-eslint/unified-signatures": "off"
};
var typeCheckedRules = {
  "@typescript-eslint/consistent-type-assertions": "off",
  "@typescript-eslint/consistent-type-definitions": "off",
  "@typescript-eslint/consistent-type-exports": "error",
  "@typescript-eslint/explicit-member-accessibility": "error",
  "@typescript-eslint/no-base-to-string": "off",
  "@typescript-eslint/no-confusing-void-expression": "off",
  "@typescript-eslint/no-deprecated": "error",
  "@typescript-eslint/no-dynamic-delete": "off",
  "@typescript-eslint/no-explicit-any": "off",
  "@typescript-eslint/no-floating-promises": "off",
  "@typescript-eslint/no-invalid-void-type": "off",
  "@typescript-eslint/no-misused-promises": "off",
  "@typescript-eslint/no-non-null-assertion": "off",
  "@typescript-eslint/no-redundant-type-constituents": "off",
  "@typescript-eslint/no-unnecessary-qualifier": "error",
  "@typescript-eslint/no-unnecessary-type-parameters": "off",
  "@typescript-eslint/no-unsafe-argument": "off",
  "@typescript-eslint/no-unsafe-assignment": "off",
  "@typescript-eslint/no-unsafe-call": "off",
  "@typescript-eslint/no-unsafe-member-access": "off",
  "@typescript-eslint/no-unsafe-return": "off",
  "@typescript-eslint/prefer-readonly": "error",
  "@typescript-eslint/prefer-regexp-exec": "error",
  "@typescript-eslint/require-array-sort-compare": ["error", { ignoreStringArrays: true }],
  "@typescript-eslint/restrict-template-expressions": ["error", { allowNumber: true, allowBoolean: true, allowAny: true, allowNullish: true, allowRegExp: true, allowNever: true }],
  "@typescript-eslint/use-unknown-in-catch-callback-variable": "off",
  "@typescript-eslint/unbound-method": "off"
};
var pluginConfigs2 = [tseslint.configs.eslintRecommended, tseslint.configs.strict, tseslint.configs.stylistic];
var pluginTypeCheckedConfigs = [tseslint.configs.strictTypeChecked, tseslint.configs.stylisticTypeChecked];
var languageOptions = (tsconfigRootDir) => ({
  parserOptions: {
    tsconfigRootDir,
    projectService: true
  }
});
var declareFilesConfig = {
  files: ["**/*.d.ts"],
  rules: {
    "no-restricted-syntax": "off"
  }
};
var jsFilesConfig = {
  files: ["**/*.js", "**/*.cjs"],
  rules: {
    "@typescript-eslint/no-require-imports": "off",
    "@typescript-eslint/no-var-requires": "off"
  }
};
function config2(tsconfigRootDir) {
  const baseConfigs = [declareFilesConfig, jsFilesConfig];
  if (!tsconfigRootDir) {
    return concat12(...pluginConfigs2, { rules: rules2 }, ...baseConfigs);
  }
  return concat12(...pluginTypeCheckedConfigs, { languageOptions: languageOptions(tsconfigRootDir) }, { rules: { ...rules2, ...typeCheckedRules } }, ...baseConfigs);
}
var typescript = async (tsconfigRootDir) => config2(tsconfigRootDir).then((configs3) => configs3.map((config3) => {
  if (isNullish(config3.files)) {
    config3.files = ["**/*.?([cm])ts?(x)"];
  }
  return config3;
}));

// src/configs/base/unused-imports.ts
import { concat as concat13 } from "eslint-flat-config-utils";
import plugin9 from "eslint-plugin-unused-imports";
var unusedImports = () => concat13(
  {
    plugins: {
      "unused-imports": plugin9
    },
    rules: {
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "unused-imports/no-unused-imports": "error",
      "unused-imports/no-unused-vars": ["error", { args: "after-used", argsIgnorePattern: "^_", vars: "all", varsIgnorePattern: "^_" }]
    }
  },
  {
    files: ["**/*.d.ts"],
    rules: {
      "unused-imports/no-unused-vars": "off"
    }
  }
);

// src/configs/tools/gitignore.ts
import plugin10 from "eslint-config-flat-gitignore";
var gitignore = () => plugin10({
  strict: false
});

// src/configs/tools/perfectionist.ts
import { concat as concat14 } from "eslint-flat-config-utils";
import plugin11 from "eslint-plugin-perfectionist";
var perfectionist = () => concat14({
  plugins: {
    perfectionist: plugin11
  },
  rules: {
    "perfectionist/sort-exports": ["error", { order: "asc", type: "natural" }],
    "perfectionist/sort-imports": ["error", { groups: ["type", ["parent-type", "sibling-type", "index-type", "internal-type"], "builtin", "external", "internal", ["parent", "sibling", "index"], "side-effect", "object", "unknown"], newlinesBetween: "ignore", order: "asc", type: "natural" }],
    "perfectionist/sort-named-exports": ["error", { order: "asc", type: "natural" }],
    "perfectionist/sort-named-imports": ["error", { order: "asc", type: "natural" }],
    "import-x/order": "off",
    "sort-imports": "off"
  }
});

// src/configs/tools/sonar.ts
import { concat as concat15 } from "eslint-flat-config-utils";
import plugin12 from "eslint-plugin-sonarjs";
var sonar = () => concat15(plugin12.configs.recommended, {
  rules: {
    "sonarjs/arguments-order": "off",
    "sonarjs/assertions-in-tests": "off",
    "sonarjs/cognitive-complexity": "off",
    "sonarjs/function-return-type": "off",
    "sonarjs/hashing": "off",
    "sonarjs/no-async-constructor": "off",
    "sonarjs/no-clear-text-protocols": "off",
    "sonarjs/no-duplicate-string": "off",
    "sonarjs/no-hardcoded-secrets": "off",
    "sonarjs/no-invariant-returns": "off",
    "sonarjs/no-nested-assignment": "off",
    "sonarjs/no-nested-conditional": "off",
    "sonarjs/no-nested-functions": "off",
    "sonarjs/no-nested-template-literals": "off",
    "sonarjs/no-os-command-from-path": "off",
    "sonarjs/pseudo-random": "off",
    "sonarjs/redundant-type-aliases": "off",
    "sonarjs/todo-tag": "off",
    "sonarjs/updated-loop-counter": "off",
    "sonarjs/no-selector-parameter": "off"
  }
});

// src/configs/tools/unicorn.ts
import { concat as concat16 } from "eslint-flat-config-utils";
import plugin13 from "eslint-plugin-unicorn";
var unicorn = () => concat16(plugin13.configs.recommended, {
  rules: {
    "unicorn/consistent-destructuring": "off",
    "unicorn/consistent-function-scoping": ["error", { checkArrowFunctions: false }],
    "unicorn/filename-case": ["error", { cases: { kebabCase: true, pascalCase: true }, ignore: [String.raw`.*\.md$`] }],
    "unicorn/import-style": "off",
    "unicorn/new-for-builtins": "off",
    "unicorn/no-array-callback-reference": "off",
    "unicorn/no-array-method-this-argument": "off",
    "unicorn/no-array-push-push": "off",
    "unicorn/no-array-reduce": "off",
    "unicorn/no-await-expression-member": "off",
    "unicorn/no-empty-file": "off",
    "unicorn/no-null": "off",
    "unicorn/no-process-exit": "off",
    "unicorn/prefer-event-target": "off",
    "unicorn/prefer-math-min-max": "off",
    "unicorn/prefer-module": "off",
    "unicorn/prefer-top-level-await": "off",
    "unicorn/prevent-abbreviations": "off",
    "unicorn/require-number-to-fixed-digits-argument": "off",
    "unicorn/switch-case-braces": ["error", "avoid"]
  }
});
export {
  antfu,
  comments,
  createRule,
  gitignore,
  ignores,
  importX,
  isBlockStatement,
  javascript,
  jsonc,
  kdt,
  kdt_default as kdtPlugin,
  node,
  perfectionist,
  promise,
  regexp,
  sonar,
  stylistic2 as stylistic,
  typescript,
  unicorn,
  unusedImports
};
//# sourceMappingURL=index.js.map