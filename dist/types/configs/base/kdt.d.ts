export declare const kdt: () => Promise<{
    plugins: {
        kdt: {
            meta: {
                name: string;
                version: string;
            };
            rules: {
                'arrow-empty-body-newline': import("../..").RuleModule<[]>;
                'import-single-line': import("../..").RuleModule<[]>;
                'object-curly-newline': import("../..").RuleModule<[(((("never" | "always") | import("../../plugins/kdt/rules").OptionObject) | import("../../plugins/kdt/rules").OptionLiterals) | undefined)?]>;
            };
        };
    };
    rules: {
        'kdt/arrow-empty-body-newline': "error";
        'kdt/import-single-line': "error";
        'kdt/object-curly-newline': "error";
        '@stylistic/object-curly-newline': "off";
    };
}[]>;
//# sourceMappingURL=kdt.d.ts.map