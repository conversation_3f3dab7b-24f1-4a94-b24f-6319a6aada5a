export declare const unusedImports: () => Promise<({
    plugins: {
        'unused-imports': import("eslint").ESLint.Plugin;
    };
    rules: {
        'no-unused-vars': "off";
        '@typescript-eslint/no-unused-vars': "off";
        'unused-imports/no-unused-imports': "error";
        'unused-imports/no-unused-vars': ["error", {
            args: string;
            argsIgnorePattern: string;
            vars: string;
            varsIgnorePattern: string;
        }];
    };
    files?: undefined;
} | {
    files: string[];
    rules: {
        'unused-imports/no-unused-vars': "off";
        'no-unused-vars'?: undefined;
        '@typescript-eslint/no-unused-vars'?: undefined;
        'unused-imports/no-unused-imports'?: undefined;
    };
    plugins?: undefined;
})[]>;
//# sourceMappingURL=unused-imports.d.ts.map